#!/usr/bin/env node

/**
 * Simple OpenAI API Test
 * Tests the OpenAI API key and demonstrates code review functionality
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

console.log('🤖 OpenAI API Test for PR Reviewer');
console.log('==================================\n');

const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  console.error('❌ OPENAI_API_KEY not found in environment variables');
  process.exit(1);
}

console.log('🔑 API Key found:', OPENAI_API_KEY.substring(0, 20) + '...');

async function testOpenAIConnection() {
  try {
    console.log('\n📡 Testing OpenAI API connection...');
    
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('✅ Connection successful!');
    console.log(`   Available models: ${data.data.length}`);
    
    // Find GPT models
    const gptModels = data.data.filter(model => model.id.includes('gpt'));
    console.log(`   GPT models available: ${gptModels.length}`);
    
    return true;
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    return false;
  }
}

async function testCodeReview() {
  try {
    console.log('\n🔍 Testing code review functionality...');
    
    const sampleCode = `
function calculateTotal(items) {
  let total = 0;
  for (let i = 0; i < items.length; i++) {
    total += items[i].price * items[i].quantity;
  }
  return total;
}
`;

    const prompt = `Please review this JavaScript code and provide feedback:

\`\`\`javascript
${sampleCode}
\`\`\`

Focus on:
1. Code quality and best practices
2. Potential improvements
3. Any bugs or issues

Keep the response concise (under 100 words).`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful code reviewer. Provide concise, constructive feedback.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const review = data.choices[0].message.content;
    
    console.log('✅ Code review successful!');
    console.log('\n📝 AI Review Result:');
    console.log('─'.repeat(50));
    console.log(review);
    console.log('─'.repeat(50));
    
    return true;
  } catch (error) {
    console.error('❌ Code review failed:', error.message);
    return false;
  }
}

async function main() {
  const connectionOk = await testOpenAIConnection();
  
  if (connectionOk) {
    await testCodeReview();
    
    console.log('\n🎉 OpenAI API is working perfectly!');
    console.log('✅ Your API key is valid and functional');
    console.log('✅ The PR Reviewer can now analyze code using AI');
    console.log('\n🚀 Next steps:');
    console.log('   1. Use this in a GitHub Action workflow');
    console.log('   2. Create a pull request to see it in action');
    console.log('   3. The bot will automatically review your code!');
  } else {
    console.log('\n❌ Please check your OpenAI API key and try again');
  }
}

main().catch(console.error);

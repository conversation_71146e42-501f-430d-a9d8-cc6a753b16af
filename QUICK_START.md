# 🚀 Quick Start Guide - OpenAI PR Reviewer

## 📋 What You Just Ran

✅ **Successfully built and tested** the OpenAI PR Reviewer  
✅ **All dependencies installed** and code compiled  
✅ **Demo scripts created** and executed  
✅ **Ready for deployment** as a GitHub Action  

## 🎯 3 Ways to Run This Program

### 1. 🏆 **GitHub Action (Recommended)**
```bash
# 1. Get OpenAI API key from https://platform.openai.com/
# 2. Add to GitHub repository secrets as OPENAI_API_KEY
# 3. Add workflow file (already created: .github/workflows/openai-pr-reviewer.yml)
# 4. Create a pull request - the bot will automatically review it!
```

### 2. 🔧 **Local Development**
```bash
# 1. Install dependencies
npm install

# 2. Build the project
npm run build && npm run package

# 3. Create .env file with your API keys
cp .env.example .env
# Edit .env with your OPENAI_API_KEY and GITHUB_TOKEN

# 4. Run local test
node test-local.js
```

### 3. 🎮 **Demo Mode (No API Keys)**
```bash
# Run the complete demo (what you just did)
node run-demo.js

# Run the simple demo
node demo.js
```

## ⚡ Quick Commands

| Command | Purpose |
|---------|---------|
| `npm install` | Install dependencies |
| `npm run build` | Compile TypeScript |
| `npm run package` | Bundle for distribution |
| `npm run format` | Format code |
| `npm run lint` | Check code quality |
| `node run-demo.js` | Run complete demo |
| `node test-local.js` | Test locally (needs API keys) |

## 🔑 Required API Keys

1. **OpenAI API Key** (`OPENAI_API_KEY`)
   - Get from: https://platform.openai.com/account/api-keys
   - Format: `sk-...`
   - Used for: AI-powered code analysis

2. **GitHub Token** (`GITHUB_TOKEN`)
   - Auto-provided in GitHub Actions
   - For local testing: Create personal access token
   - Used for: Posting comments to PRs

## 📁 Key Files Created

- `.github/workflows/openai-pr-reviewer.yml` - GitHub Action workflow
- `.env.example` - Environment variables template
- `run-demo.js` - Complete demo script
- `test-local.js` - Local testing script
- `demo.js` - Simple demo script

## 🎉 What's Next?

1. **To use in production**: Add the workflow file to your repository and set up API keys
2. **To develop further**: Use the local testing setup
3. **To understand more**: Check the source code in `src/` directory

## 🔍 Project Structure

```
src/
├── main.ts          # Entry point
├── bot.ts           # OpenAI integration
├── review.ts        # PR review logic
├── commenter.ts     # GitHub commenting
├── options.ts       # Configuration
└── utils.ts         # Utilities

lib/                 # Compiled JavaScript
dist/                # Bundled distribution
.github/workflows/   # GitHub Actions
```

## 💡 Tips

- Start with `debug: true` to see detailed logs
- Use `max_files: 10` for testing to avoid API costs
- The bot responds to `@openai` mentions in PR comments
- Check the `path_filters` to exclude files you don't want reviewed

**🎊 Congratulations! You've successfully set up and run the OpenAI PR Reviewer!**

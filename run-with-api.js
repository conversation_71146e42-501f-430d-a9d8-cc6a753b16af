#!/usr/bin/env node

/**
 * OpenAI PR Reviewer - Live Demo with API
 * Demonstrates actual AI code review functionality
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🚀 OpenAI PR Reviewer - Live Demo');
console.log('=================================\n');

const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  console.error('❌ OPENAI_API_KEY not found');
  process.exit(1);
}

console.log('🔑 Using API Key:', OPENAI_API_KEY.substring(0, 20) + '...');

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function reviewCode(code, filename) {
  try {
    console.log(`\n🔍 Reviewing ${filename}...`);
    
    // Add delay to avoid rate limits
    await delay(1000);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert code reviewer. Provide concise, helpful feedback on code quality, potential issues, and improvements. Keep responses under 80 words.'
          },
          {
            role: 'user',
            content: `Please review this ${filename} code:\n\n\`\`\`\n${code}\n\`\`\``
          }
        ],
        max_tokens: 150,
        temperature: 0.1
      })
    });

    if (response.status === 429) {
      console.log('⏳ Rate limit hit, waiting 10 seconds...');
      await delay(10000);
      return await reviewCode(code, filename); // Retry
    }

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
    
  } catch (error) {
    console.error(`❌ Error reviewing ${filename}:`, error.message);
    return `Error: ${error.message}`;
  }
}

async function demonstrateReview() {
  console.log('\n📋 Demonstrating AI Code Review Capabilities');
  console.log('============================================');
  
  // Sample code files to review
  const codeFiles = [
    {
      filename: 'calculator.js',
      code: `function add(a, b) {
  return a + b;
}

function divide(a, b) {
  return a / b;
}`
    },
    {
      filename: 'user.py',
      code: `def get_user_data(user_id):
    if user_id:
        return database.query("SELECT * FROM users WHERE id = " + str(user_id))
    return None`
    }
  ];

  for (const file of codeFiles) {
    const review = await reviewCode(file.code, file.filename);
    
    console.log(`\n📄 File: ${file.filename}`);
    console.log('─'.repeat(40));
    console.log('Code:');
    console.log(file.code);
    console.log('\n🤖 AI Review:');
    console.log(review);
    console.log('─'.repeat(40));
  }
}

async function main() {
  try {
    // Test connection first
    console.log('\n📡 Testing API connection...');
    const testResponse = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      }
    });
    
    if (testResponse.ok) {
      console.log('✅ API connection successful!');
      
      // Run the demo
      await demonstrateReview();
      
      console.log('\n🎉 Demo completed successfully!');
      console.log('\n✨ What you just saw:');
      console.log('   ✅ Real OpenAI API integration');
      console.log('   ✅ Actual AI code review');
      console.log('   ✅ Multiple programming languages');
      console.log('   ✅ Rate limit handling');
      
      console.log('\n🚀 Ready for GitHub Actions!');
      console.log('   Your API key works perfectly');
      console.log('   The PR Reviewer is fully functional');
      
    } else {
      console.log('❌ API connection failed');
      console.log(`   Status: ${testResponse.status}`);
    }
    
  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    
    if (error.message.includes('429')) {
      console.log('\n💡 Rate limit reached. This is normal for new API keys.');
      console.log('   Wait a few minutes and try again, or use in GitHub Actions');
      console.log('   where requests are naturally spaced out.');
    }
  }
}

main();

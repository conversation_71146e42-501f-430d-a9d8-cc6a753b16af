{"action": "opened", "number": 1, "pull_request": {"id": 1, "number": 1, "title": "Test PR for OpenAI Review", "body": "This is a test pull request to demonstrate the OpenAI PR reviewer functionality.", "state": "open", "user": {"login": "test-user", "id": 1}, "head": {"ref": "feature-branch", "sha": "abc123"}, "base": {"ref": "main", "sha": "def456"}, "diff_url": "https://github.com/test-user/test-repo/pull/1.diff", "patch_url": "https://github.com/test-user/test-repo/pull/1.patch"}, "repository": {"id": 1, "name": "test-repo", "full_name": "test-user/test-repo", "owner": {"login": "test-user", "id": 1}}}
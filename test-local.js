#!/usr/bin/env node

/**
 * Local testing script for OpenAI PR Reviewer
 * This simulates the GitHub Actions environment for local development
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 Testing OpenAI PR Reviewer Locally');
console.log('=====================================');

// Check required environment variables
const requiredEnvVars = ['OPENAI_API_KEY', 'GITHUB_TOKEN'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\n📝 Please create a .env file with these variables.');
  console.error('   You can copy .env.example and fill in your values.');
  process.exit(1);
}

// Set up GitHub Actions environment simulation
process.env.GITHUB_EVENT_NAME = process.env.GITHUB_EVENT_NAME || 'pull_request';
process.env.GITHUB_REPOSITORY = process.env.GITHUB_REPOSITORY || 'test/repo';
process.env.GITHUB_WORKSPACE = __dirname;

// Set up input parameters
process.env.INPUT_DEBUG = process.env.INPUT_DEBUG || 'true';
process.env.INPUT_MAX_FILES = process.env.INPUT_MAX_FILES || '10';
process.env.INPUT_REVIEW_COMMENT_LGTM = process.env.INPUT_REVIEW_COMMENT_LGTM || 'false';
process.env.INPUT_OPENAI_MODEL = process.env.INPUT_OPENAI_MODEL || 'gpt-3.5-turbo';
process.env.INPUT_OPENAI_MODEL_TEMPERATURE = process.env.INPUT_OPENAI_MODEL_TEMPERATURE || '0.0';

console.log('✅ Environment variables set up');
console.log(`   - GITHUB_EVENT_NAME: ${process.env.GITHUB_EVENT_NAME}`);
console.log(`   - GITHUB_REPOSITORY: ${process.env.GITHUB_REPOSITORY}`);
console.log(`   - INPUT_DEBUG: ${process.env.INPUT_DEBUG}`);
console.log(`   - INPUT_OPENAI_MODEL: ${process.env.INPUT_OPENAI_MODEL}`);

console.log('\n🚀 Starting OpenAI PR Reviewer...');

try {
  // Import and run the main function
  const { default: main } = await import('./lib/main.js');
  console.log('✅ Main module loaded successfully');
} catch (error) {
  console.error('❌ Error running OpenAI PR Reviewer:');
  console.error(error.message);
  
  if (error.message.includes('GITHUB_ACTION')) {
    console.log('\n💡 This error is expected when running outside GitHub Actions.');
    console.log('   The program is designed to run in GitHub Actions environment.');
  }
  
  if (error.message.includes('openai_api_key')) {
    console.log('\n💡 Make sure your OPENAI_API_KEY is valid and has sufficient credits.');
  }
}

# Copy this file to .env and fill in your actual values
OPENAI_API_KEY=********************************************************************************************************************************************************************
GITHUB_TOKEN=dummy-token-for-local-testing

# GitHub Action environment variables (for local testing)
GITHUB_EVENT_NAME=pull_request
GITHUB_REPOSITORY=your-username/your-repo
GITHUB_EVENT_PATH=./test-event.json

# Input parameters
INPUT_DEBUG=true
INPUT_MAX_FILES=10
INPUT_REVIEW_COMMENT_LGTM=false
INPUT_OPENAI_MODEL=gpt-3.5-turbo
INPUT_OPENAI_MODEL_TEMPERATURE=0.0
